package api

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"

	goshopify "github.com/bold-commerce/go-shopify"
	"github.com/jangnh/amote/internal/shopify"
	"github.com/jangnh/amote/model"
	"github.com/labstack/echo/v4"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (api *Api) ThemeActivateCartDrawer(c echo.Context) error {
	return c.NoContent(http.StatusOK)
}

func (api *Api) VerifyCartDrawer(c echo.Context) error {
	shop := c.Get("shop").(string)
	store, err := api.Repo.Store.FindOne(shop)
	if err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	client := goshopify.NewClient(goshopify.App{}, store.Shop, store.AccessToken, goshopify.WithVersion("2023-04"))
	themes, err := client.Theme.List(nil)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	var themeMain goshopify.Theme
	for _, theme := range themes {
		if theme.Role == "main" {
			themeMain = theme
			break
		}
	}
	// get theme settings
	settings, err := client.Asset.Get(themeMain.ID, "config/settings_data.json")
	if err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	var settingData model.SettingData
	err = json.Unmarshal([]byte(settings.Value), &settingData)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	currentSetting, ok := settingData.Current.(map[string]interface{})
	if ok {
		// "current" is objects
		cartType := currentSetting["cart_type"]
		if cartType == "drawer" {
			return c.NoContent(http.StatusOK)
		}
	}
	presets := settingData.Presets
	m, ok := presets.(map[string]interface{})
	if !ok {
		return c.JSON(http.StatusNotFound, ok)
	}
	presetValue := m["Default"]
	v, ok := presetValue.(map[string]interface{})
	if !ok {
		return c.JSON(http.StatusNoContent, ok)
	}
	cartType := v["cart_type"]
	if cartType == "drawer" {
		return c.NoContent(http.StatusOK)
	}
	return c.NoContent(http.StatusNoContent)
}

func (api *Api) VerifyAppBlock(c echo.Context) error {
	shop := c.Get("shop").(string)
	store, _ := api.Repo.Store.FindOne(shop)
	shopifyClient := shopify.New(store.Shop, store.AccessToken, "2025-04")
	theme, err := shopifyClient.GetMainTheme(context.Background())
	if err != nil {
		slog.Error(fmt.Sprintf("get main theme error %s", err.Error()))
		return c.JSON(200, ApiResponse{
			Status: false,
			Error:  fmt.Sprintf("get main theme error %s", err.Error()),
		})
	}
	// check theme compatibility in background
	compatibility, err := shopifyClient.IsThemeAppBlockCompatible(context.Background(), theme.ID)
	if err != nil {
		slog.Error(fmt.Sprintf("check theme compatibility shop %s error %s", store.Shop, err.Error()))
	}
	themeVersion := "v1"
	if compatibility {
		themeVersion = "v2"
	}
	api.Repo.Store.Collection.UpdateOne(context.TODO(),
		primitive.M{"shop": store.Shop},
		primitive.M{"$set": primitive.M{"theme_version": themeVersion, "theme_id": theme.ID, "theme_name": theme.Name}})

	if compatibility {
		return c.NoContent(http.StatusOK)
	}
	return c.NoContent(http.StatusNoContent)
}

// get app blocks and app embed with status in theme
type ThemeAppExtension map[string]bool

type Block struct {
	Type     string `json:"type"`
	Disabled bool   `json:"disabled"`
}

type ThemeConfigSettingData struct {
	Current struct {
		Blocks map[string]Block `json:"blocks"`
	} `json:"current"`
}

func (api *Api) GetThemeAppExtensionStatus(c echo.Context) error {
	themeAppExtensionId := api.Server.Config.AppBlockID
	shop := c.Get("shop").(string)
	store, err := api.Repo.Store.FindOne(shop)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}
	accessToken := store.AccessToken
	themeId := store.ThemeID
	client := shopify.New(shop, accessToken, api.Server.Config.ShopifyVersion)
	ctx := context.Background()
	themeAppExtensionStatus, err := client.GetThemeAppExtension(ctx, themeId, themeAppExtensionId)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}
	return c.JSON(http.StatusOK, ApiResponse{
		Status: true,
		Data:   themeAppExtensionStatus,
	})
}

func (api *Api) GetMainTheme(c echo.Context) error {
	shop := c.Get("shop").(string)
	store, err := api.Repo.Store.FindOne(shop)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}
	client := shopify.New(store.Shop, store.AccessToken, "2025-04")
	ctx := context.Background()
	theme, err := client.GetMainTheme(ctx)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}
	return c.JSON(http.StatusOK, ApiResponse{
		Status: true,
		Data:   theme,
	})
}

func (api *Api) CheckThemeCompatibility(c echo.Context) error {
	shop := c.Get("shop").(string)
	store, err := api.Repo.Store.FindOne(shop)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}
	client := shopify.New(store.Shop, store.AccessToken, "2025-04")
	ctx := context.Background()
	themeId := store.ThemeID
	compatibility, err := client.IsThemeAppBlockCompatible(ctx, themeId)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Error:  err.Error(),
		})
	}
	return c.JSON(http.StatusOK, ApiResponse{
		Status: true,
		Data:   compatibility,
	})
}

func (api *Api) initThemeRoute() {
	api.AdminRoute.Theme.GET("/verify_cart_drawer", api.VerifyCartDrawer)
	api.AdminRoute.Theme.GET("/activate_cart_drawer", api.ThemeActivateCartDrawer)
	api.AdminRoute.Theme.GET("/verify_support_app_block", api.VerifyAppBlock)
	api.AdminRoute.Theme.GET("/app_extension_status", api.GetThemeAppExtensionStatus)
	api.AdminRoute.Theme.GET("/check_theme_compatibility", api.CheckThemeCompatibility)
	api.AdminRoute.Theme.GET("", api.GetMainTheme)
}
