package store

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/jangnh/amote/model"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DB struct {
	Client   *mongo.Client
	Database *mongo.Database
}

func NewDB(dsn string, dbName string) *DB {
	serverAPIOptions := options.ServerAPI(options.ServerAPIVersion1)
	clientOptions := options.Client().ApplyURI(dsn).SetServerAPIOptions(serverAPIOptions)
	client, _ := mongo.Connect(context.TODO(), clientOptions)
	db := client.Database(dbName)
	return &DB{
		Client:   client,
		Database: db,
	}
}

func (db *DB) Ping() error {
	return db.Client.Ping(context.TODO(), nil)
}

func (db *DB) Shutdown() error {
	return db.Client.Disconnect(context.Background())
}

func (db *DB) GetGoogleOauthCredentials() model.AuthCredential {
	collection := db.Database.Collection("oauth_credentials")
	filter := primitive.D{{"vendor", "google"}}
	credential := collection.FindOne(context.TODO(), filter)
	result := model.AuthCredential{}
	credential.Decode(&result)
	return result
}

func (db *DB) SetGoogleOauthToken(token string) {
	db.Database.Collection("oauth_credentials").FindOneAndUpdate(context.TODO(), bson.D{{"vendor", "google"}}, bson.D{{"$set", bson.D{{"token", token}}}})
}

func (db *DB) SetPubsubHistoryId(history uint64) {
	db.Database.Collection("oauth_credentials").FindOneAndUpdate(context.TODO(), bson.D{{"vendor", "google"}}, bson.D{{"$set", bson.D{{"historyId", history}}}})
}

func (db *DB) GetPubsubHistoryId() uint64 {
	type MessageData struct {
		HistoryId uint64 `bson:"historyId"`
	}
	var result MessageData
	db.Database.Collection("oauth_credentials").FindOne(context.TODO(), primitive.M{"vendor": "google"}).Decode(&result)
	return result.HistoryId
}
