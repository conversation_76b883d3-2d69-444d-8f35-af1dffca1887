package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Banner struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	Name          string             `json:"name" bson:"name"`
	Type          string             `json:"type" bson:"type"`
	Page          string             `json:"page" bson:"page"`
	ScheduleStart string             `json:"schedule_start" bson:"schedule_start"`
	ScheduleEnd   string             `json:"schedule_end" bson:"schedule_end"`
	Position      string             `json:"position" bson:"position"`
	Blacklist     struct {
		Shop bool `json:"shop" bson:"shop"`
		Comp bool `json:"comp" bson:"comp"`
	} `json:"blacklist" bson:"blacklist"`
	Title       string    `json:"title" bson:"title"`
	Description string    `json:"description" bson:"description"`
	Badge       string    `json:"badge" bson:"badge"`
	Rating      string    `json:"rating" bson:"rating"`
	InstallTag  string    `json:"install_tag" bson:"install_tag"`
	PriceTag    string    `json:"price_tag" bson:"price_tag"`
	CTA1        string    `json:"cta_1" bson:"cta_1"`
	Link1       string    `json:"link_1" bson:"link_1"`
	CTA2        string    `json:"cta_2" bson:"cta_2"`
	Link2       string    `json:"link_2" bson:"link_2"`
	Logo        string    `json:"logo" bson:"logo"`
	Image       string    `json:"image" bson:"image"`
	Status      string    `json:"status" bson:"status"`
	AppName     string    `json:"app_name" bson:"app_name"`
	CreatedAt   time.Time `json:"created_at" bson:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" bson:"updated_at"`
}
