package model

import (
	"slices"
	"strings"
)

type Blacklist struct {
	Competitor bool `json:"comp" bson:"comp"` // Competitor
	Shopify    bool `json:"shop" bson:"shop"` // Shopify
}

type Loyalty struct {
	Status            string `json:"loyalty_status" bson:"loyalty_status"` // 0: if loyalty_plan = 0 & loyalty_quest=0, 1: loyalty_plan = 1 or loyalty_quest = 1, change depend on loyalty_plan and loyalty_quest
	LoyaltyPlan       string `json:"loyalty_plan" bson:"loyalty_plan"`     //  0: khi plan khong ho tro loyalty (essential), 1: khi plan ho tro loyalty (premium) // default 1
	LoyaltyQuest      string `json:"loyalty_quest" bson:"loyalty_quest"`   // 0: khi quest_1: 0|1|2 or quest_2: 0 or quest_3: 0, 1: khi quest_1: 3,4,5 or quest_2: 1 or quest_3: 1
	Quest1            string `json:"quest_1" bson:"quest_1"`               // 0: chua review. 1,2: da review, chua done quest. 3,4,5: da review, done quest
	Quest2            string `json:"quest_2" bson:"quest_2"`               // 0: chua verify quest thanh cong, 1: verify quest thanh cong (extension_verified)
	Quest3            string `json:"quest_3" bson:"quest_3"`               // 0: chua verify quest thanh cong, 1: verify quest thanh cong (product_verified)
	ApplicationStatus string `json:"application_status" bson:"application_status"`
}
type LoyaltyQuest struct {
	Quest1 string `json:"quest_1" bson:"quest_1"` // 0: chua review. 1,2: da review, chua done quest. 3,4,5: da review, done quest
	Quest2 string `json:"quest_2" bson:"quest_2"` // 0: chua verify quest thanh cong, 1: verify quest thanh cong (extension_verified)
	Quest3 string `json:"quest_3" bson:"quest_3"` // 0: chua verify quest thanh cong, 1: verify quest thanh cong (product_verified)
}

type Metadata struct {
	FeaturePin []string `json:"features_pin" bson:"features_pin"`
}

type Store struct {
	StoreName       string `json:"store_name"`
	Currency        string `json:"currency"`
	ShopifyPlanName string `json:"shopify_plan_name"` // shopify plan
	PrimaryLocale   string `json:"primary_locale"`
	CountryCode     string `json:"country_code"`
	Shop            string `json:"shop,omitempty"`
	Timezone        string `json:"timezone"`
	IanaTimezone    string `json:"iana_timezone"`

	IsAllowTestCharge bool    `json:"is_allow_test_charge"`
	Loyalty           Loyalty `json:"loyalty"`

	Blacklist Blacklist `json:"bl"`

	Metadata Metadata `json:"metadata"`

	EmailReceiveNotification string    `json:"email_receive_notification"`
	AppEmbed                 bool      `json:"app_embed"`
	IsBanned                 bool      `json:"is_banned,omitempty"`
	RequireExtension         bool      `json:"re"`
	ThemeCompatible          bool      `json:"theme_compatible"`
	Blocked                  bool      `json:"blocked"`
	Banner                   *[]Banner `json:"banner,omitempty"`
}

func (s *Store) IsBlockDev() bool {
	planDev := []string{"partner_test", "affiliate", "plus_partner_sandbox"}
	return (slices.Contains(planDev, s.ShopifyPlanName) || strings.Contains(s.ShopifyPlanName, "sandbox")) && !s.IsAllowTestCharge
}
