# TrustZ

This project contains the source code of both Backend and Frontend.

## Project Structure

- `cmd`: Contains commands to run the application. For example, `cmd/server/main.go` is the entry point to start the API server. **Required**.
- `config`: Contains packages to load and parse configurations for the project. **Optional**.
- `internal`: Contains internal packages used exclusively within this project. **Required**.
    - `models`: Contains data object definitions, data models from data sources, or ORMs. **Optional**.
    - `repositories`: Contains repository structs and functions to interact with databases. **Optional**.
    - `require`: Contains essential packages that the services cannot run without. **Required**.
    - `services`: Defines the services of this API, such as user services and order services.
    - `utils`: Contains utility packages. **Optional**.
- `pkg`: Contains packages that are exposed for use by other services or clients. **Optional**.
- `storefront_script`: Frontend code
- `web`: Frontend code